using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Akka.Actor;
using HeroYulgang.Core.Actors;
using HeroYulgang.Core.Network;
using HeroYulgang.Services;
using HeroYulgang.Helpers;
using RxjhServer;
using RxjhServer.HelperTools;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor xử lý các gói tin - đã hợp nhất với PlayerPacketHandlerActor
    /// </summary>
    public class PacketHandlerActor : ReceiveActor
    {
        private readonly Dictionary<PacketType, Func<IActorRef, ClientSession, Packet, Task>> _handlers = [];

        // Thêm các thuộc tính để xử lý player packets
        private Players _player;
        private ActorNetState _actorNetState;
        private IActorRef _clientConnection;

        public PacketHandlerActor()
        {
            // Đăng ký các message handler
            Receive<ProcessPacket>(HandlePacket);
            Receive<ProcessPlayerPacket>(HandlePlayerPacket);
            Receive<SetPlayerContext>(SetPlayerContext);
            RegisterHandlers();
        }

        private void RegisterHandlers()
        {
            // Đăng ký các handler cho từng loại gói tin
            //_handlers[PacketType.Ping] = HandlePingAsync;
             _handlers[PacketType.Login] = HandleLoginAsync;
            // _handlers[PacketType.CharacterList] = HandleCharacterListAsync;
            // _handlers[PacketType.CreateCharacter] = HandleCreateCharacterAsync;
            // _handlers[PacketType.DeleteCharacter] = HandleDeleteCharacterAsync;
            // _handlers[PacketType.EnterGame] = HandleEnterGameAsync;
            // _handlers[PacketType.Chat] = HandleChatAsync;
            // _handlers[PacketType.Move] = HandleMoveAsync;
            // _handlers[PacketType.Attack] = HandleAttackAsync;
            // _handlers[PacketType.UseSkill] = HandleUseSkillAsync;
            // _handlers[PacketType.UseItem] = HandleUseItemAsync;
            // _handlers[PacketType.PickupItem] = HandlePickupItemAsync;
            // _handlers[PacketType.DropItem] = HandleDropItemAsync;
            // _handlers[PacketType.EquipItem] = HandleEquipItemAsync;
            // _handlers[PacketType.UnequipItem] = HandleUnequipItemAsync;
            // _handlers[PacketType.TradeRequest] = HandleTradeRequestAsync;
            // _handlers[PacketType.TradeAccept] = HandleTradeAcceptAsync;
            // _handlers[PacketType.TradeCancel] = HandleTradeCancelAsync;
            // _handlers[PacketType.TradeComplete] = HandleTradeCompleteAsync;
            // Đăng ký các handler khác tại đây
        }

        private async Task HandleLoginAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            // Xử lý gói tin Login
            // Giả sử dữ liệu gói tin: [username length][username][password length][password]
            try
            {
                var reader = new BinaryReader(new MemoryStream(packet.Data));
                reader.BaseStream.Seek(14, SeekOrigin.Begin);
                byte[] userNameByte = reader.ReadBytes(14);
                string username = System.Text.Encoding.GetEncoding(World.Language_Charset).GetString(userNameByte).Trim().Replace("\0", String.Empty);

                Logger.Instance.Debug($"Yêu cầu đăng nhập từ {username}");

                // TODO: Xác thực người dùng với cơ sở dữ liệu

                var player = new Players
                {
                    UserName = username,
                    LanIp = username
                };
                // Giả sử xác thực thành công
                session.IsAuthenticated = true;
                session.AccountId = username;

                // Tạo ActorNetState trực tiếp trong PacketHandlerActor
                var actorNetState = new ActorNetState(connection, session.SessionId, session.RemoteEndPoint);

                // Thiết lập Client cho player
                player.Client = actorNetState;
                actorNetState.Player = player;

                // Thiết lập player context
                _player = player;
                _actorNetState = actorNetState;
                _clientConnection = connection;

                // Thông báo cho ClientActor về player reference
                var clientActorPath = $"/user/tcpManager/client-{session.SessionId}";
                var clientActor = Context.ActorSelection(clientActorPath);
                Console.WriteLine(clientActorPath);
                clientActor.Tell(new SetPlayerReference(player));

                // await World.Instance.loginServerClient.SendTransmitMessageAsync("Test Message");

                // Xử lý packet đăng nhập trực tiếp
                ProcessPlayerPacketData(packet.Data, packet.Data.Length);
                Console.WriteLine("Đã xử lý packet đăng nhập trực tiếp trong PacketHandlerActor");

                Logger.Instance.Info($"Người dùng {username} đã đăng nhập thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin đăng nhập: {ex.Message}");

                // Gửi phản hồi thất bại
                byte[] responseData = BitConverter.GetBytes(0); // 0 = thất bại
                var response = new Packet(PacketType.Login, responseData);
                await SendPacketAsync(connection, response.ToByteArray());
            }
        }


        /// <summary>
        /// Thiết lập context cho player packet handling
        /// </summary>
        private void SetPlayerContext(SetPlayerContext message)
        {
            _player = message.Player;
            _actorNetState = message.ActorNetState;
            _clientConnection = message.ClientConnection;

            Logger.Instance.Debug($"Đã thiết lập player context cho {_player?.UserName}");
        }

        /// <summary>
        /// Xử lý ProcessPlayerPacket message
        /// </summary>
        private void HandlePlayerPacket(ProcessPlayerPacket message)
        {
            try
            {
                if (_player == null)
                {
                    Logger.Instance.Warning("Nhận ProcessPlayerPacket nhưng _player là null");
                    return;
                }

                // Kiểm tra _player.Client thay vì _actorNetState
                if (_player.Client == null)
                {
                    Logger.Instance.Warning($"Nhận ProcessPlayerPacket nhưng _player.Client là null cho player {_player.UserName}");
                    return;
                }

                if (_clientConnection == null)
                {
                    Logger.Instance.Warning("Nhận ProcessPlayerPacket nhưng _clientConnection là null");
                    return;
                }

                // Cập nhật _actorNetState từ _player.Client
                _actorNetState = _player.Client as ActorNetState;

                Logger.Instance.Debug($"Đã nhận ProcessPlayerPacket cho {_player.UserName}");

                // Xử lý packet giống như PlayerPacketHandlerActor
                ProcessPlayerPacketData(message.Data, message.Length);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin từ người chơi {_player?.UserName}: {ex.Message}");
            }
        }

        private async void HandlePacket(ProcessPacket message)
        {
            try
            {
                var packet = Packet.Parse(message.Data, message.Data.Length);

                // Ghi log gói tin đã phân tích
                PacketLogger.LogPacket(message.Session.SessionId, packet, true);

                if (_handlers.TryGetValue(packet.Type, out var handler))
                {
                    await handler(message.Connection, message.Session, packet);
                }
                else
                {
                    Logger.Instance.Warning($"Không có handler cho gói tin loại {packet.Type} từ session {message.Session.SessionId}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin từ session {message.Session.SessionId}: {ex.Message}");
            }
        }

        // Các phương thức xử lý gói tin

        // Helper method để gửi gói tin
        private Task SendPacketAsync(IActorRef connection, byte[] data)
        {
            // Gửi gói tin đến connection
            Context.Parent.Tell(new SendPacket(connection, data));

            // Ghi log gói tin gửi đi
            if (data.Length >= 2)
            {
                // Tìm session ID từ connection
                var tcpManager = Context.Parent;
                tcpManager.Tell(new GetSessionIdFromConnection(connection, sessionId =>
                {
                    if (sessionId != -1)
                    {
                        PacketLogger.LogOutgoingPacket(sessionId, data);
                    }
                }));
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Xử lý packet data cho player - được chuyển từ PlayerPacketHandlerActor
        /// </summary>
        private void ProcessPlayerPacketData(byte[] data, int length)
        {
            if (_player == null)
            {
                Logger.Instance.Warning("ProcessPlayerPacketData: _player là null");
                return;
            }

            // Kiểm tra _player.Client trước khi sử dụng
            if (_player.Client == null)
            {
                Logger.Instance.Warning($"ProcessPlayerPacketData: _player.Client là null cho player {_player.UserName}");
                return;
            }

            // Lấy packet type từ data
            int packetType = BitConverter.ToInt16(data, 8);

            try
            {
                // cut byte 6-7 from data
                var oldPacket = new byte[length - 2];
                Buffer.BlockCopy(data, 0, oldPacket, 0, 6);
                Buffer.BlockCopy(data,8, oldPacket, 6, length - 8);
                // Kiểm tra xem người chơi đã kết nối chưa
                if (!World.allConnectedChars.TryGetValue(_player.SessionID, out var _))
                {
                    HandlePreLoginPackets(packetType, oldPacket, length);
                    return;
                }

                // Xử lý heartbeat riêng
                if (packetType == 176)
                {
                    _player.Phat_Hien_Nhip_Tim(oldPacket, oldPacket.Length);
                    return;
                }

                // Kiểm tra kết nối thành công
                if (!_player._connectionSucceeded)
                {
                    throw new Exception("Connection not succeeded");
                }

                // Xử lý các loại packet khác
                HandleGamePackets(packetType, oldPacket, oldPacket.Length);

                // Cập nhật trạng thái di chuyển
                if (packetType != 7)
                {
                    _player.NhanVatDangDiChuyen = false;
                }

                // Xử lý xác nhận tấn công
                if (packetType != 16666 && length != 36 && packetType == 9 && _player.ThoiGianXacNhanTanCong != 0)
                {
                    _player.AttackConfirmation_Apply++;
                }
            }
            catch (Exception ex)
            {
                // Kiểm tra _player.Client trước khi truy cập thuộc tính
                string clientInfo = "null";
                string worldId = "unknown";

                try
                {
                    if (_player.Client != null)
                    {
                        worldId = _player.Client.WorldId?.ToString() ?? "null";
                        clientInfo = _player.Client.ToString() ?? "null";
                    }
                }
                catch (Exception clientEx)
                {
                    Logger.Instance.Error($"Lỗi khi truy cập thông tin client: {clientEx.Message}");
                }

                LogHelper.WriteLine(LogLevel.Error, $"Manage Packet() Lỗi tại case: [{packetType}]-[{worldId}]-[{clientInfo}] [{ex}]");
                Console.WriteLine(ex);

                // Kiểm tra trước khi dispose
                try
                {
                    if (_player.Client != null)
                    {
                        _player.Client.Dispose();
                    }
                }
                catch (Exception disposeEx)
                {
                    Logger.Instance.Error($"Lỗi khi dispose client: {disposeEx.Message}");
                }

                logo.logdis($"Disconnected![{_player.Userid}]-[{_player.UserName}][Mã dis 10]");
            }
        }

        /// <summary>
        /// Xử lý các packet trước khi đăng nhập
        /// </summary>
        private void HandlePreLoginPackets(int packetType, byte[] data, int length)
        {
            switch (packetType)
            {
                case 20:
                    _player.CreateCharacter(data, length);
                    break;
                case 16:
                    _player.GetAListOfPeople(data, length);
                    break;
                case 1:
                    _player.KetNoi_DangNhap(data, length);
                    break;
                case 3:
                    _player.DangXuat(data, length);
                    break;
                case 5:
                    _player.CharacterLogin(data, length);
                    break;
                case 143:
                    _player.Display();
                    break;
                case 56:
                    _player.KiemTraNhanVat_CoTonTaiHayKhong(data, length);
                    break;
                case 30:
                    _player.XoaBoNhanVat(data, length);
                    break;
                case 836:
                    _player.XacMinhThongTinDangNhapId(data, length);
                    break;
                case 218:
                case 211:
                    _player.ChangeLineVerification(data, length);
                    break;
                case 16666:
                    _player.IsAttackConfirmation(data, length);
                    break;
                case 5638:
                case 8212:
                    _player.VersionVerification(data, length);
                    break;
            }
        }

        /// <summary>
        /// Xử lý các packet trong game
        /// </summary>
        private void HandleGamePackets(int packetType, byte[] data, int length)
        {
            // Chuyển tiếp đến phương thức xử lý packet tương ứng trong Players
            // Đây chỉ là một số ví dụ, cần bổ sung đầy đủ các case
            switch (packetType)
            {
                case 14:
                    _player.ThrowItems(data, length);
                    break;
                case 3:
                    _player.DangXuat(data, length);
                    break;
                case 7:
                    _player.CharacterMove(data, length);
                    break;
                case 8:
                    _player.Speak(data, length);
                    break;
                case 9:
                    {
                        _player.Attack(data, length);
                        break;
                    }
                case 11:
                    _player.PickUpItems(data, length);
                    break;
                // Thêm các case khác tương ứng với ManagePacket trong Players
                default:
                    // Chuyển tiếp đến phương thức ManagePacket gốc để xử lý các packet khác
                    _player.ManagePacket(data, length + 2);
                    break;
            }
        }
    }

    /// <summary>
    /// Message để thiết lập player context cho PacketHandlerActor
    /// </summary>
    public class SetPlayerContext
    {
        public Players Player { get; }
        public ActorNetState ActorNetState { get; }
        public IActorRef ClientConnection { get; }

        public SetPlayerContext(Players player, ActorNetState actorNetState, IActorRef clientConnection)
        {
            Player = player;
            ActorNetState = actorNetState;
            ClientConnection = clientConnection;
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin cho người chơi
    /// </summary>
    public class ProcessPlayerPacket
    {
        public byte[] Data { get; }
        public int Length { get; }

        public ProcessPlayerPacket(byte[] data, int length)
        {
            Data = data;
            Length = length;
        }
    }
}
